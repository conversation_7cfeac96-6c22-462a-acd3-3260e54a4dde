# 管理员订单列表接口文档

## 接口概述

管理员订单列表接口允许具有管理员权限的用户查询和过滤所有订单数据，支持多种过滤条件和分页功能。

## 接口信息

- **URL**: `GET /api/v1/order-service/admin/orders`
- **权限**: 需要管理员权限（admin 或 super_admin 角色）
- **认证**: 需要在请求头中包含 `x-user-id` 和 `x-role`

## 请求参数

### 分页参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | int | 否 | 50 | 每页数量，范围：1-500（可配置） |
| offset | int | 否 | 0 | 偏移量，从0开始 |

### 过滤参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | string | 否 | 用户ID过滤 |
| currency | string | 否 | 货币代码过滤（如：USD、CNY） |
| pay_status | string | 否 | 支付状态过滤 |
| payed_method | string | 否 | 支付方式过滤 |
| psp_provider | string | 否 | 支付服务提供商过滤 |
| payed_at_start | string | 否 | 支付时间开始（RFC3339格式） |
| payed_at_end | string | 否 | 支付时间结束（RFC3339格式） |
| refund_status | string | 否 | 退款状态过滤 |
| refunded_at_start | string | 否 | 退款时间开始（RFC3339格式） |
| refunded_at_end | string | 否 | 退款时间结束（RFC3339格式） |
| psp_price_id | string | 否 | PSP价格ID过滤 |
| psp_customer_email | string | 否 | PSP客户邮箱过滤 |
| psp_subscription_id | string | 否 | PSP订阅ID过滤 |

### 支付状态枚举值

- `created` - 已创建
- `paid` - 已支付
- `succeeded` - 支付成功
- `failed` - 支付失败
- `cancelled` - 已取消

### 退款状态枚举值

- `none` - 无退款
- `partial` - 部分退款
- `refunded` - 已退款

## 响应格式

```json
{
  "orders": [
    {
      "id": 1,
      "order_id": "20250710153045999stripe1234567890123456789",
      "user_id": "user123",
      "product_id": "prod_123",
      "product_desc": "Premium Subscription",
      "price_id": "price_456",
      "quantity": 1,
      "amount": 99.99,
      "net_amount": 99.99,
      "currency": "USD",
      "pay_status": "succeeded",
      "payed_method": "stripe",
      "psp_provider": "stripe",
      "card_number": "**** 4242",
      "payed_at": "2025-07-10T15:35:00Z",
      "refund_status": "none",
      "refunded_at": null,
      "psp_product_id": "prod_stripe_123",
      "psp_product_desc": "Stripe Product",
      "psp_price_id": "price_stripe_456",
      "psp_payment_id": "pi_stripe_789",
      "psp_customer_id": "cus_stripe_abc",
      "psp_customer_email": "<EMAIL>",
      "psp_subscription_id": "sub_stripe_def",
      "created_at": "2025-07-10T15:30:45Z",
      "updated_at": "2025-07-10T15:35:00Z",
      "deleted": false,
      "deleted_at": null
    }
  ],
  "pagination": {
    "total": 150,
    "limit": 20,
    "offset": 0,
    "remaining": 130
  }
}
```

## 请求示例

### Linux/macOS curl 命令

#### 1. 获取所有订单（默认分页）

```bash
curl -X GET "http://localhost:8080/api/v1/order-service/admin/orders" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

#### 2. 按用户ID过滤

```bash
curl -X GET "http://localhost:8080/api/v1/order-service/admin/orders?user_id=user123" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

#### 3. 按支付状态和货币过滤

```bash
curl -X GET "http://localhost:8080/api/v1/order-service/admin/orders?pay_status=succeeded&currency=USD" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

#### 4. 按时间范围过滤

```bash
curl -X GET "http://localhost:8080/api/v1/order-service/admin/orders?payed_at_start=2025-07-01T00:00:00Z&payed_at_end=2025-07-31T23:59:59Z" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

#### 5. 自定义分页

```bash
curl -X GET "http://localhost:8080/api/v1/order-service/admin/orders?limit=50&offset=100" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

#### 6. 复合过滤条件

```bash
curl -X GET "http://localhost:8080/api/v1/order-service/admin/orders?psp_provider=stripe&pay_status=succeeded&limit=10&offset=0" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

### Windows PowerShell 命令

#### 1. 获取所有订单（默认分页）

```powershell
Invoke-RestMethod -Uri "http://localhost:8080/api/v1/order-service/admin/orders" `
  -Method GET `
  -Headers @{
    "X-User-ID" = "admin123"
    "X-Role" = "admin"
  }
```

#### 2. 按用户ID过滤

```powershell
Invoke-RestMethod -Uri "http://localhost:8080/api/v1/order-service/admin/orders?user_id=user123" `
  -Method GET `
  -Headers @{
    "X-User-ID" = "admin123"
    "X-Role" = "admin"
  }
```

#### 3. 按支付状态和货币过滤

```powershell
Invoke-RestMethod -Uri "http://localhost:8080/api/v1/order-service/admin/orders?pay_status=succeeded&currency=USD" `
  -Method GET `
  -Headers @{
    "X-User-ID" = "admin123"
    "X-Role" = "admin"
  }
```

#### 4. 按时间范围过滤

```powershell
$uri = "http://localhost:8080/api/v1/order-service/admin/orders?payed_at_start=2025-07-01T00:00:00Z&payed_at_end=2025-07-31T23:59:59Z"
Invoke-RestMethod -Uri $uri `
  -Method GET `
  -Headers @{
    "X-User-ID" = "admin123"
    "X-Role" = "admin"
  }
```

#### 5. 自定义分页

```powershell
Invoke-RestMethod -Uri "http://localhost:8080/api/v1/order-service/admin/orders?limit=50&offset=100" `
  -Method GET `
  -Headers @{
    "X-User-ID" = "admin123"
    "X-Role" = "admin"
  }
```

#### 6. 复合过滤条件

```powershell
$uri = "http://localhost:8080/api/v1/order-service/admin/orders?psp_provider=stripe&pay_status=succeeded&limit=10&offset=0"
Invoke-RestMethod -Uri $uri `
  -Method GET `
  -Headers @{
    "X-User-ID" = "admin123"
    "X-Role" = "admin"
  }
```

## 错误响应

### 401 未授权

```json
{
  "error": "unauthorized",
  "message": "Missing user ID in request headers"
}
```

### 403 权限不足

```json
{
  "error": "forbidden",
  "message": "Insufficient permissions"
}
```

### 500 服务器错误

```json
{
  "error": "Internal server error message"
}
```

## 配置管理员角色和分页

管理员角色和分页参数在配置文件中定义：

```yaml
# config.yaml
admin:
  allowed_roles:
    - "admin"
    - "super_admin"
  pagination:
    default_limit: 50  # 默认每页数量
    max_limit: 500     # 最大每页数量
```

也可以通过环境变量配置：

```bash
# 角色配置
export PAYMENT_ADMIN_ALLOWED_ROLES="admin,super_admin"

# 分页配置
export PAYMENT_ADMIN_PAGINATION_DEFAULT_LIMIT=50
export PAYMENT_ADMIN_PAGINATION_MAX_LIMIT=500
```

## 分页说明

### 分页逻辑

1. **limit**: 每页返回的记录数，范围 1-500（可配置），默认 50（可配置）
2. **offset**: 跳过的记录数，从 0 开始，默认 0
3. **total**: 符合条件的总记录数
4. **remaining**: 剩余未返回的记录数

### 分页计算示例

假设总共有 150 条记录：

- 第1页：`limit=20&offset=0` → 返回记录 1-20，remaining=130
- 第2页：`limit=20&offset=20` → 返回记录 21-40，remaining=110
- 第3页：`limit=20&offset=40` → 返回记录 41-60，remaining=90
- ...
- 最后一页：`limit=20&offset=140` → 返回记录 141-150，remaining=0

## 性能优化建议

1. **使用索引字段过滤**: 优先使用已建立索引的字段进行过滤，如 user_id、pay_status、psp_provider 等
2. **合理设置分页大小**: 建议每页不超过 100 条记录，以保证响应速度。可通过配置文件调整默认值和最大值
3. **时间范围过滤**: 使用时间范围过滤时，建议范围不要过大，避免查询超时
4. **避免深度分页**: 当 offset 很大时，查询性能会下降，建议使用其他分页策略
5. **分页配置优化**: 根据实际业务需求调整 `admin.pagination.default_limit` 和 `admin.pagination.max_limit` 配置

## 注意事项

1. 所有时间参数必须使用 RFC3339 格式（如：2025-07-10T15:30:45Z）
2. 过滤条件之间是 AND 关系，即同时满足所有指定条件
3. 字符串过滤是精确匹配，不支持模糊查询
4. 分页参数超出范围时会自动调整到有效范围内
5. 管理员角色和分页配置修改后需要重启服务才能生效
6. 分页限制可通过配置文件调整，默认最大值为 500，默认每页 50 条记录
