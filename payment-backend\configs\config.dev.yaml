# 开发环境配置

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30
  write_timeout: 30
  mode: "debug"

# 数据库配置（开发环境使用MySQL数据库）
database:
  driver: "mysql"
  host: "*************"
  # host: "**************"
  port: 3306
  username: "root"
  password: "123456"
  database: "aibook_payment"
  ssl_mode: "disable"

# 支付配置（开发环境使用模拟网关）
payment:
  providers:
    paypal:
      enabled: true
      api_key: "mock_paypal_api_key"
      secret_key: "mock_paypal_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "http://localhost:8080/api/v1/pay-service/webhooks/paypal"
        secret: "mock_paypal_webhook_secret"
      settings:
        environment: "sandbox"

    stripe:
      enabled: true
      api_key: "sk_test_51RbdvCC53MAl6WmqszVyfUOFJqVBbMNnhJAC4hVyEORaD7uecrN6rqpVNtvezPXabFhZkillLEt0VwMGyhUWIvvA00BCdee8is"
      secret_key: "mock_stripe_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "http://localhost:8080/api/v1/pay-service/webhooks/stripe"
        secret: "mock_stripe_webhook_secret"
      settings:
        environment: "test"

# 日志配置
log:
  level: "debug"
  format: "console"
  output: "stdout"
  filename: ""
  max_size: 100
  max_backups: 3
  max_age: 28

# 雪花算法配置
snowflake:
  node_id: 1

# Dubbo配置（开发环境）
dubbo:
  enabled: true # 开发环境启用Dubbo服务
  port: 20000
  ip: "0.0.0.0"
  registry:
    address: "nacos://127.0.0.1:8848"
    namespace: "payment-service-dev"
    username: "nacos"
    password: "nacos"
